<template>
  <div
    class="native-select"
    :class="[
      selectSize ? 'native-select--' + selectSize : '',
      multiple ? 'native-select-multiple' : '',
      { 'is-focus': visible }
    ]"
    @click.stop="toggleMenu"
    v-clickoutside="handleClose"
  >
    <!-- 多选标签 -->
    <div
      class="native-select__tags"
      v-if="multiple"
      ref="tags"
      :style="{ 'max-width': inputWidth - 32 + 'px', width: '100%' }"
    >
      <span
        v-for="item in selected"
        :key="getValueKey(item)"
        class="native-select__tag"
      >
        <span class="native-select__tag-text">{{ item.currentLabel }}</span>
        <i
          v-if="!selectDisabled"
          class="hos-icon-close"
          @click.stop="deleteTag(item)"
        ></i>
      </span>

      <!-- 可输入的 input -->
      <input
        type="text"
        class="native-select__input"
        :class="[selectSize ? `is-${selectSize}` : '']"
        :disabled="selectDisabled"
        :placeholder="currentPlaceholder"
        @focus="handleFocus"
        @blur="softFocus = false"
        @keydown.down.prevent="handleNavigate('next')"
        @keydown.up.prevent="handleNavigate('prev')"
        @keydown.enter.prevent="selectOption"
        @keydown.esc.stop.prevent="visible = false"
        @keydown.delete="deletePrevTag"
        @keydown.tab="visible = false"
        v-model="query"
        @input="handleQueryChange"
        v-if="filterable"
        ref="input"
      />
    </div>

    <!-- 单选输入框 -->
    <div
      v-if="!multiple"
      class="native-select__input-wrapper"
      :class="{ 'is-focus': visible }"
    >
      <input
        ref="reference"
        v-model="selectedLabel"
        type="text"
        :placeholder="currentPlaceholder"
        :name="name"
        :disabled="selectDisabled"
        :readonly="readonly"
        class="native-select__inner"
        @focus="handleFocus"
        @blur="handleBlur"
        @input="handleInputChange"
        @keydown.down.stop.prevent="handleNavigate('next')"
        @keydown.up.stop.prevent="handleNavigate('prev')"
        @keydown.enter.prevent="selectOption"
        @keydown.esc.stop.prevent="visible = false"
        @keydown.tab="visible = false"
      />
      <i
        class="native-select__caret hos-icon-arrow-down"
        :class="{ 'is-reverse': visible }"
      ></i>
      <i
        v-if="showClose"
        class="native-select__clear hos-icon-circle-close"
        @click.stop="handleClearClick"
      ></i>
    </div>

    <!-- 下拉菜单 -->
    <transition name="native-zoom-in-top">
      <div
        v-show="visible && !loading"
        class="native-select-dropdown"
        ref="popper"
      >
        <div class="native-select-dropdown__wrap">
          <ul
            class="native-select-dropdown__list"
            :class="{ 'is-empty': !allowCreate && query && filteredOptionsCount === 0 }"
          >
            <slot></slot>
            <!-- allow-create 创建的新选项 -->
            <li
              v-if="showNewOption"
              class="native-select-dropdown__item"
              @click="handleCreateOption"
            >
              <span>创建 "{{ query }}"</span>
            </li>
            <!-- 调试信息 -->
            <li v-if="filterable && allowCreate && query" class="native-select-dropdown__item" style="color: red; font-size: 12px;">
              调试: query='{{ query }}', visible={{ visible }}, showNewOption={{ showNewOption }}
            </li>
          </ul>
        </div>
        <!-- 空状态 -->
        <div
          v-if="emptyText && (!allowCreate || loading || (allowCreate && options.length === 0))"
          class="native-select-dropdown__empty"
        >
          {{ emptyText }}
        </div>
      </div>
    </transition>
  </div>
</template>

<script>
// clickoutside 指令
const clickoutside = {
  bind(el, binding, vnode) {
    function documentHandler(e) {
      if (el.contains(e.target)) {
        return false
      }
      if (binding.expression) {
        binding.value(e)
      }
    }
    el.__vueClickOutside__ = documentHandler
    document.addEventListener('click', documentHandler)
  },
  unbind(el, binding) {
    document.removeEventListener('click', el.__vueClickOutside__)
    delete el.__vueClickOutside__
  }
}

// 简化的 debounce 函数
function debounce(func, wait) {
  let timeout
  return function executedFunction(...args) {
    const later = () => {
      clearTimeout(timeout)
      func(...args)
    }
    clearTimeout(timeout)
    timeout = setTimeout(later, wait)
  }
}

export default {
  name: 'NativeSelect',
  componentName: 'NativeSelect',
  
  directives: {
    clickoutside
  },
  
  provide() {
    return {
      'select': this
    }
  },
  
  props: {
    value: {},
    multiple: Boolean,
    disabled: Boolean,
    clearable: Boolean,
    filterable: Boolean,
    allowCreate: Boolean,
    loading: Boolean,
    placeholder: {
      type: String,
      default: '请选择'
    },
    name: String,
    size: String,
    popperClass: String,
    remote: Boolean,
    valueKey: {
      type: String,
      default: 'value'
    },
    defaultFirstOption: Boolean,
    multipleLimit: {
      type: Number,
      default: 0
    },
    noDataText: {
      type: String,
      default: '无数据'
    }
  },
  
  data() {
    return {
      options: [],
      cachedOptions: [],
      createdLabel: null,
      createdSelected: false,
      selected: this.multiple ? [] : {},
      inputLength: 20,
      inputWidth: 0,
      initialInputHeight: 28,
      optionsCount: 0,
      filteredOptionsCount: 0,
      visible: false,
      softFocus: false,
      selectedLabel: '',
      hoverIndex: -1,
      query: '',
      previousQuery: null,
      inputHovering: false,
      currentPlaceholder: '',
      menuVisibleOnFocus: false,
      isOnComposition: false,
      isSilentBlur: false
    }
  },
  
  computed: {
    readonly() {
      return !this.filterable || this.multiple || (!this.visible && this.filterable)
    },
    
    showClose() {
      let hasValue = this.multiple
        ? Array.isArray(this.value) && this.value.length > 0
        : this.value !== undefined && this.value !== null && this.value !== ''
      let criteria = this.clearable &&
        !this.selectDisabled &&
        this.inputHovering &&
        hasValue
      return criteria
    },
    
    selectSize() {
      return this.size
    },
    
    selectDisabled() {
      return this.disabled
    },
    
    emptyText() {
      if (this.loading) {
        return '加载中'
      } else {
        if (this.remote && this.query === '' && this.options.length === 0) return false
        if (this.filterable && this.query && this.options.length > 0 && this.filteredOptionsCount === 0) {
          return '无匹配数据'
        }
        if (this.options.length === 0) {
          return this.noDataText
        }
      }
      return null
    },
    
    showNewOption() {
      // 必须满足基本条件：可过滤、允许创建、有查询内容
      if (!this.filterable || !this.allowCreate || !this.query) {
        return false
      }
      
      const trimmedQuery = this.query.trim()
      if (!trimmedQuery) {
        return false
      }
      
      // 检查当前输入的值是否已经在已选择的值中
      if (this.multiple) {
        const currentValues = typeof this.value === 'string' 
          ? this.value.split(',').filter(item => item.trim())
          : (Array.isArray(this.value) ? this.value : [])
        if (currentValues.includes(trimmedQuery)) {
          return false
        }
      }
      
      // 检查是否与现有选项重复（只检查可见的选项）
      let hasExistingOption = this.options.filter(option => !option.created && option.visible)
        .some(option => option.currentLabel === trimmedQuery || option.value === trimmedQuery)
      
      return !hasExistingOption
    },
    
    hoverOption() {
      return this.options[this.hoverIndex]
    },
    
    optionsAllDisabled() {
      return this.options.filter(option => option.visible).every(option => option.disabled)
    }
  },
  
  watch: {
    selectDisabled() {
      this.$nextTick(() => {
        this.resetInputHeight()
      })
    },
    
    placeholder(val) {
      this.cachedPlaceHolder = this.currentPlaceholder = val
    },
    
    value(val, oldVal) {
      if (this.multiple) {
        this.resetInputHeight()
        // 修正多选模式下placeholder的显示逻辑
        let hasSelectedItems = false
        if (Array.isArray(val)) {
          hasSelectedItems = val.length > 0
        } else if (typeof val === 'string') {
          hasSelectedItems = val.trim().length > 0
        }
        
        if (hasSelectedItems || (this.$refs.input && this.query !== '')) {
          this.currentPlaceholder = ''
        } else {
          this.currentPlaceholder = this.cachedPlaceHolder
        }
        if (val !== oldVal) {
          this.$emit('change', val)
        }
        // 重新设置选中项
        this.setSelected()
      } else {
        this.setSelected()
        if (val !== oldVal) {
          this.$emit('change', val)
        }
      }
    },
    
    visible(val) {
      if (!val) {
        this.broadcast('NativeOption', 'queryChange', '')
        this.handleIconHide()
        this.broadcast('NativeOption', 'queryChange', '')
        if (this.$refs.input) {
          this.$refs.input.blur()
        }
        this.query = ''
        this.previousQuery = null
        this.selectedLabel = ''
        this.inputLength = 20
        this.menuVisibleOnFocus = false
        this.resetHoverIndex()
        this.$nextTick(() => {
          if (this.$refs.input &&
            this.$refs.input.value === '' &&
            this.selected.length === 0) {
            this.currentPlaceholder = this.cachedPlaceHolder
          }
        })
        if (!this.multiple) {
          if (this.selected) {
            if (this.filterable && this.allowCreate &&
              this.createdSelected && this.createdLabel) {
              this.selectedLabel = this.createdLabel
            } else {
              this.selectedLabel = this.selected.currentLabel
            }
            if (this.filterable) this.query = this.selectedLabel
          }
        }
      } else {
        this.handleIconShow()
        this.broadcast('NativeOption', 'queryChange', this.query)
        if (this.filterable) {
          this.query = this.remote ? '' : this.selectedLabel
          this.handleQueryChange(this.query)
          if (this.multiple) {
            this.$refs.input.focus()
          } else {
            if (!this.remote) {
              this.broadcast('NativeOption', 'queryChange', '')
              this.broadcast('NativeOption', 'queryChange', this.query)
            }
          }
        }
      }
      this.$emit('visible-change', val)
    },
    
    options() {
      if (this.$isServer) return
      
      // options 数组变化
      
      this.$nextTick(() => {
        this.broadcast('NativeOption', 'queryChange', '')
      })
      let inputs = this.$el.querySelectorAll('input')
      if ([].indexOf.call(inputs, document.activeElement) === -1) {
        this.setSelected()
      }
      if (this.defaultFirstOption && (this.filterable || this.remote) && this.filteredOptionsCount) {
        this.checkDefaultFirstOption()
      }
    }
  },
  
  methods: {
    handleComposition(event) {
      const text = event.target.value
      if (event.type === 'compositionend') {
        this.isOnComposition = false
        this.$nextTick(_ => this.handleQueryChange(text))
      } else {
        const lastCharacter = text[text.length - 1] || ''
        this.isOnComposition = !this.isKorean(lastCharacter)
      }
    },
    
    isKorean(text) {
      const reg = /([(\uAC00-\uD7AF)|(\u3130-\u318F)])+/gi
      return reg.test(text)
    },
    
    handleQueryChange(val) {
      if (this.previousQuery === val || this.isOnComposition) return
      if (
        this.previousQuery === null &&
        (typeof this.filterMethod === 'function' || typeof this.remoteMethod === 'function')
      ) {
        this.previousQuery = val
        return
      }
      this.previousQuery = val
      
      // 确保在有输入时显示下拉框（特别是 allow-create 模式）
      if (val && this.filterable && this.allowCreate && !this.visible) {
        this.visible = true
      }
      
      this.$nextTick(() => {
        if (this.visible) this.broadcast('NativeOption', 'queryChange', val)
      })
      this.hoverIndex = -1
      if (this.multiple && this.filterable) {
        this.$nextTick(() => {
          const length = this.$refs.input.value.length * 15 + 20
          this.inputLength = this.collapseTags ? Math.min(50, length) : length
          this.managePlaceholder()
          this.resetInputHeight()
        })
      }
      if (this.remote && typeof this.remoteMethod === 'function') {
        this.hoverIndex = -1
        this.remoteMethod(val)
      } else if (typeof this.filterMethod === 'function') {
        this.filterMethod(val)
        this.broadcast('NativeOptionGroup', 'queryChange')
      } else {
        this.filteredOptionsCount = this.optionsCount
        this.broadcast('NativeOption', 'queryChange', val)
        this.broadcast('NativeOptionGroup', 'queryChange')
      }
      if (this.defaultFirstOption && (this.filterable || this.remote) &&
        this.filteredOptionsCount) {
        this.checkDefaultFirstOption()
      }
    },
    
    managePlaceholder() {
      if (this.multiple) {
        // 多选模式下，根据已选择的项和当前输入来决定placeholder显示
        let hasSelectedItems = false
        if (Array.isArray(this.value)) {
          hasSelectedItems = this.value.length > 0
        } else if (typeof this.value === 'string') {
          hasSelectedItems = this.value.trim().length > 0
        }
        
        const hasInput = this.$refs.input && this.$refs.input.value
        this.currentPlaceholder = (hasSelectedItems || hasInput) ? '' : this.cachedPlaceHolder
      } else {
        if (this.currentPlaceholder !== '') {
          this.currentPlaceholder = this.$refs.input.value ? '' : this.cachedPlaceHolder
        }
      }
    },
    
    checkDefaultFirstOption() {
      this.hoverIndex = -1
      // highlight the created option
      let hasCreated = false
      for (let i = this.options.length - 1; i >= 0; i--) {
        if (this.options[i].created) {
          hasCreated = true
          this.hoverIndex = i
          break
        }
      }
      if (hasCreated) return
      for (let i = 0; i !== this.options.length; ++i) {
        const option = this.options[i]
        if (this.query) {
          if (option.currentLabel.toLowerCase().indexOf(this.query.toLowerCase()) > -1) {
            this.hoverIndex = i
            break
          }
        } else {
          this.hoverIndex = i
          break
        }
      }
    },
    
    handleInputChange() {
      if (this.filterable && this.query !== this.selectedLabel) {
        this.query = this.selectedLabel
        this.handleQueryChange(this.query)
      }
    },
    
    toggleMenu() {
      if (!this.selectDisabled) {
        if (this.menuVisibleOnFocus) {
          this.menuVisibleOnFocus = false
        } else {
          this.visible = !this.visible
        }
        if (this.visible) {
          (this.$refs.input || this.$refs.reference).focus()
        }
      }
    },
    
    selectOption() {
      if (!this.visible) {
        this.toggleMenu()
      } else {
        if (this.options[this.hoverIndex]) {
          this.handleOptionSelect(this.options[this.hoverIndex])
        } else if (this.showNewOption) {
          this.handleCreateOption()
        } else if (this.allowCreate && this.query && this.query.trim()) {
          // 强制创建选项，即使 showNewOption 为 false
          this.handleCreateOption()
        }
      }
    },
    
    handleCreateOption() {
      const trimmedQuery = this.query.trim()
      if (!trimmedQuery) return
      
      if (this.multiple) {
        // 兼容 input-multi 组件，需要发送字符串格式
        const currentValues = typeof this.value === 'string' 
          ? this.value.split(',').filter(item => item.trim())
          : (Array.isArray(this.value) ? this.value : [])
        
        if (!currentValues.includes(trimmedQuery)) {
          const newValues = [...currentValues, trimmedQuery]
          const newValueString = newValues.join(',')
          this.$emit('input', newValueString)
          
          // 立即更新 selected 数组
          this.$nextTick(() => {
            this.setSelected()
          })
        }
        
        // 清空查询但保持焦点和下拉框打开状态
        this.query = ''
        this.previousQuery = ''
        
        // 确保下拉框保持打开并保持焦点
        this.$nextTick(() => {
          this.visible = true
          if (this.$refs.input) {
            this.$refs.input.focus()
          }
        })
      } else {
        this.$emit('input', trimmedQuery)
        this.query = ''
        this.visible = false
      }
    },
    
    deleteTag(option) {
      let index = this.selected.indexOf(option)
      if (index > -1 && !this.selectDisabled) {
        // 兼容 input-multi 组件，处理字符串格式
        const currentValues = typeof this.value === 'string' 
          ? this.value.split(',').filter(item => item.trim())
          : (Array.isArray(this.value) ? this.value : [])
        
        currentValues.splice(index, 1)
        const newValueString = currentValues.join(',')
        
        this.$emit('input', newValueString)
        this.$emit('remove-tag', option.value)
        // 立即更新 selected 数组
        this.$nextTick(() => {
          this.setSelected()
        })
      }
    },
    
    deletePrevTag(e) {
      if (e.target.value.length <= 0 && !this.toggleLastOptionHitState()) {
        const value = this.value.slice()
        value.pop()
        this.$emit('input', value)
      }
    },
    
    resetInputState(e) {
      if (e.keyCode !== 8) this.toggleLastOptionHitState(false)
    },
    
    toggleLastOptionHitState(hit) {
      if (!Array.isArray(this.selected)) return
      const option = this.selected[this.selected.length - 1]
      if (!option) return

      if (hit === true || hit === false) {
        option.hitState = hit
        return hit
      }

      option.hitState = !option.hitState
      return option.hitState
    },
    
    handleFocus(event) {
      if (!this.softFocus) {
        if (this.automaticDropdown || this.filterable) {
          this.visible = true
          if (this.filterable) {
            this.menuVisibleOnFocus = true
          }
        }
        this.$emit('focus', event)
      } else {
        this.softFocus = false
      }
    },
    
    blur() {
      this.visible = false
      this.$refs.reference.blur()
    },
    
    handleBlur(event) {
      setTimeout(() => {
        if (this.isSilentBlur) {
          this.isSilentBlur = false
        } else {
          this.$emit('blur', event)
        }
      }, 50)
      this.softFocus = false
    },
    
    handleClearClick(event) {
      this.deleteSelected(event)
    },
    
    deleteSelected(event) {
      event.stopPropagation()
      const value = this.multiple ? [] : ''
      this.$emit('input', value)
      this.visible = false
      this.$emit('clear')
    },
    
    handleClose() {
      this.visible = false
    },
    
    handleIconHide() {
      // placeholder
    },
    
    handleIconShow() {
      // placeholder
    },
    
    handleNavigate(direction) {
      if (this.isOnComposition) return

      this.navigateOptions(direction)
    },
    
    navigateOptions(direction) {
      if (!this.visible) {
        this.visible = true
        return
      }
      if (this.options.length === 0 || this.filteredOptionsCount === 0) return
      if (!this.optionsAllDisabled) {
        if (direction === 'next') {
          this.hoverIndex++
          if (this.hoverIndex === this.options.length) {
            this.hoverIndex = 0
          }
        } else if (direction === 'prev') {
          this.hoverIndex--
          if (this.hoverIndex < 0) {
            this.hoverIndex = this.options.length - 1
          }
        }
        const option = this.options[this.hoverIndex]
        if (option.disabled === true ||
          option.groupDisabled === true ||
          !option.visible) {
          this.navigateOptions(direction)
        }
        this.$nextTick(() => this.scrollToOption(this.hoverOption))
      }
    },
    
    scrollToOption(option) {
      // 简化实现
    },
    
    resetInputHeight() {
      if (this.collapseTags && !this.filterable) return
      this.$nextTick(() => {
        if (!this.$refs.reference) return
        const inputChildNodes = this.$refs.reference.childNodes
        let inputChild = [].filter.call(inputChildNodes, item => item.tagName === 'INPUT')[0]
        const tags = this.$refs.tags
        const sizeInMap = this.initialInputHeight || 40
        inputChild.style.height = this.selected.length === 0
          ? sizeInMap + 'px'
          : Math.max(
            tags ? (tags.clientHeight + (tags.clientHeight > sizeInMap ? 6 : 0)) : 0,
            sizeInMap
          ) + 'px'
        if (this.visible && this.emptyText !== false) {
          this.broadcast('NativeOption', 'queryChange', '')
        }
      })
    },
    
    resetHoverIndex() {
      setTimeout(() => {
        if (!this.multiple) {
          this.hoverIndex = this.options.indexOf(this.selected)
        } else {
          if (this.selected.length > 0) {
            this.hoverIndex = Math.min.apply(null, this.selected.map(item => this.options.indexOf(item)))
          } else {
            this.hoverIndex = -1
          }
        }
      }, 300)
    },
    
    handleOptionSelect(option, byClick) {
      if (this.multiple) {
        const value = (this.value || []).slice()
        const optionIndex = this.getValueIndex(value, option.value)
        if (optionIndex > -1) {
          value.splice(optionIndex, 1)
        } else if (this.multipleLimit <= 0 || value.length < this.multipleLimit) {
          value.push(option.value)
        }
        this.$emit('input', value)
        if (option.created) {
          this.query = ''
          this.handleQueryChange('')
          this.inputLength = 20
        }
        if (this.filterable) this.$refs.input.focus()
      } else {
        this.$emit('input', option.value)
        this.visible = false
      }
      this.isSilentBlur = byClick
      this.setSoftFocus()
      if (this.visible) return
      this.$nextTick(() => {
        this.scrollToOption(option)
      })
    },
    
    setSoftFocus() {
      this.softFocus = true
      const input = this.$refs.input || this.$refs.reference
      if (input) {
        input.focus()
      }
    },
    
    getValueIndex(arr = [], value) {
      const isObject = Object.prototype.toString.call(value).toLowerCase() === '[object object]'
      if (!isObject) {
        return arr.indexOf(value)
      } else {
        const valueKey = this.valueKey
        let index = -1
        arr.some((item, i) => {
          if (this.getValueByPath(item, valueKey) === this.getValueByPath(value, valueKey)) {
            index = i
            return true
          }
          return false
        })
        return index
      }
    },
    
    getValueByPath(object, prop) {
      prop = prop || ''
      const paths = prop.split('.')
      let current = object
      let result = null
      for (let i = 0, j = paths.length; i < j; i++) {
        const path = paths[i]
        if (!current) break

        if (i === j - 1) {
          result = current[path]
          break
        }
        current = current[path]
      }
      return result
    },
    
    getValueKey(item) {
      if (Object.prototype.toString.call(item.value).toLowerCase() !== '[object object]') {
        return item.value
      } else {
        return this.getValueByPath(item.value, this.valueKey)
      }
    },
    
    onOptionDestroy(index) {
      if (index > -1) {
        this.optionsCount--
        this.filteredOptionsCount--
        this.options.splice(index, 1)
      }
    },
    
    resetInputWidth() {
      if (this.$refs.reference) {
        this.inputWidth = this.$refs.reference.getBoundingClientRect().width
      }
    },
    
    setSelected() {
      if (!this.multiple) {
        let option = this.getOption(this.value)
        if (option) {
          this.selectedLabel = option.currentLabel
          this.selected = option
          return
        } else {
          this.selectedLabel = this.value
          this.selected = {
            currentLabel: this.value,
            value: this.value
          }
          return
        }
      }
      let result = []
      if (Array.isArray(this.value)) {
        this.value.forEach(value => {
          result.push(this.getOption(value) || { currentLabel: value, value: value })
        })
      } else if (typeof this.value === 'string' && this.value) {
        // 处理字符串格式输入（兼容 input-multi 组件）
        const values = this.value.split(',').filter(item => item.trim())
        values.forEach(value => {
          const trimmedValue = value.trim()
          result.push(this.getOption(trimmedValue) || { currentLabel: trimmedValue, value: trimmedValue })
        })
      }
      this.selected = result
      this.$nextTick(() => {
        this.resetInputHeight()
      })
    },
    
    getOption(value) {
      let option
      const isObject = Object.prototype.toString.call(value).toLowerCase() === '[object object]'
      const isNull = Object.prototype.toString.call(value).toLowerCase() === '[object null]'
      const isUndefined = Object.prototype.toString.call(value).toLowerCase() === '[object undefined]'

      for (let i = this.cachedOptions.length - 1; i >= 0; i--) {
        const cachedOption = this.cachedOptions[i]
        const isEqual = isObject
          ? this.getValueByPath(cachedOption.value, this.valueKey) === this.getValueByPath(value, this.valueKey)
          : cachedOption.value === value
        if (isEqual) {
          option = cachedOption
          break
        }
      }
      if (option) return option
      const label = (!isObject && !isNull && !isUndefined)
        ? String(value) : value
      let newOption = {
        value: value,
        currentLabel: label
      }
      if (this.multiple) {
        newOption.hitState = false
      }
      return newOption
    },
    
    broadcast(componentName, eventName, ...params) {
      this.$children.forEach(child => {
        var name = child.$options.componentName

        if (name === componentName) {
          child.$emit.apply(child, [eventName].concat(params))
        } else {
          // todo 如果 params 是空数组，接收的时候会接收到 undefined
          this.broadcast.apply(child, [componentName, eventName].concat([params]))
        }
      })
    }
  },
  
  created() {
    this.cachedPlaceHolder = this.currentPlaceholder = this.placeholder
    if (this.multiple && !Array.isArray(this.value)) {
      this.$emit('input', [])
    }
    if (!this.multiple && Array.isArray(this.value)) {
      this.$emit('input', '')
    }

    this.debouncedQueryChange = debounce(300, (val) => {
      this.handleQueryChange(val)
    })

    this.$on('handleOptionClick', this.handleOptionSelect)
    this.$on('setSelected', this.setSelected)
  },
  
  mounted() {
    // 初始化placeholder显示逻辑
    if (this.multiple) {
      let hasSelectedItems = false
      if (Array.isArray(this.value)) {
        hasSelectedItems = this.value.length > 0
      } else if (typeof this.value === 'string') {
        hasSelectedItems = this.value.trim().length > 0
      }
      
      if (hasSelectedItems) {
        this.currentPlaceholder = ''
      } else {
        this.currentPlaceholder = this.cachedPlaceHolder
      }
    }
    this.$nextTick(() => {
      this.resetInputWidth()
      this.setSelected()
      
    })
  }
}
</script>

<style lang="scss">
// 引入样式文件
@import '../styles/select.scss';
</style>