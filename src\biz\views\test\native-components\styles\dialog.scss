// 原生 Dialog 组件样式 - 基于 hosui 样式

// 遮罩层动画
.v-modal-enter-active {
  animation: v-modal-in .2s ease;
}

.v-modal-leave-active {
  animation: v-modal-out .2s ease forwards;
}

.v-modal-enter {
  opacity: 0;
}

.v-modal-leave-to {
  opacity: 0;
}

@keyframes v-modal-in {
  0% {
    opacity: 0;
  }
  100% {
    opacity: 0.5;
  }
}

@keyframes v-modal-out {
  0% {
    opacity: 0.5;
  }
  100% {
    opacity: 0;
  }
}

// 遮罩层
.v-modal {
  position: fixed;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  opacity: 0.5;
  background: #000;
  z-index: 2000;
}

// 对话框包装器
.native-dialog__wrapper {
  position: fixed;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  overflow: auto;
  margin: 0;
  z-index: 2001;
}

// 对话框主体
.native-dialog {
  position: relative;
  margin: 0 auto 50px;
  background: #FFF;
  border-radius: 2px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, .3);
  box-sizing: border-box;
  width: 50%;

  // 全屏模式
  &.is-fullscreen {
    width: 100%;
    margin-top: 0;
    margin-bottom: 0;
    height: 100%;
    overflow: auto;
  }

  // 居中模式
  &--center {
    text-align: center;

    .native-dialog__body {
      text-align: initial;
      padding: 25px 20px 30px;
    }

    .native-dialog__footer {
      text-align: inherit;
    }
  }
}

// 对话框头部
.native-dialog__header {
  padding: 15px;
  height: 18px;
  position: relative;
  border-bottom: 1px solid #E4E7ED;
  
  // 当只有关闭按钮时，减少内边距
  &:empty {
    padding: 10px 15px;
    height: auto;
  }
  
  // 如果没有标题文本，也减少内边距
  .native-dialog__title:empty + .native-dialog__headerbtn {
    top: 10px;
  }
}

.native-dialog__title {
  line-height: 16px;
  font-size: 16px;
  font-weight: 700;
  color: #000;
}

// 头部按钮
.native-dialog__headerbtn {
  position: absolute;
  top: 15px;
  right: 15px;
  padding: 0;
  background: transparent;
  border: none;
  outline: 0;
  cursor: pointer;
  font-size: 16px;

  .native-dialog__close {
    color: #909399;
    transition: color .2s;
    font-size: 18px;
    font-weight: 400;
    line-height: 1;
    // 使用更好看的关闭符号
    &::before {
      content: "✕";
    }
    
    // 如果 hos-icon-close 类可用，则使用图标字体
    &.hos-icon-close::before {
      content: "\e6db";
      font-family: hosui-icons;
    }
  }

  &:focus .native-dialog__close,
  &:hover .native-dialog__close {
    color: #409EFF;
  }
}

// 对话框内容
.native-dialog__body {
  padding: 0;
  color: #666;
  font-size: 14px;
  word-break: break-all;
}

// 对话框底部
.native-dialog__footer {
  padding: 10px 15px 15px;
  box-sizing: border-box;
  text-align: center;
}

// 对话框动画
.dialog-fade-enter-active {
  animation: dialog-fade-in .3s;
}

.dialog-fade-leave-active {
  animation: dialog-fade-out .3s;
}

@keyframes dialog-fade-in {
  0% {
    transform: translate3d(0, -20px, 0);
    opacity: 0;
  }
  100% {
    transform: translate3d(0, 0, 0);
    opacity: 1;
  }
}

@keyframes dialog-fade-out {
  0% {
    transform: translate3d(0, 0, 0);
    opacity: 1;
  }
  100% {
    transform: translate3d(0, -20px, 0);
    opacity: 0;
  }
}

// 防止页面滚动
.native-popup-parent--hidden {
  overflow: hidden;
}

// 响应式设计
@media (max-width: 768px) {
  .native-dialog {
    width: 90%;
    margin: 30px auto;
  }

  .native-dialog__header {
    padding: 10px;
  }

  .native-dialog__title {
    font-size: 14px;
  }

  .native-dialog__headerbtn {
    top: 10px;
    right: 10px;
    font-size: 14px;
  }

  .native-dialog__body {
    padding: 10px;
    font-size: 13px;
  }

  .native-dialog__footer {
    padding: 10px;
  }
}

// 小屏幕全屏显示
@media (max-width: 480px) {
  .native-dialog:not(.is-fullscreen) {
    width: 95%;
    margin: 20px auto;
  }
}
