// Native Select 样式 - 基于 HosUI Select 迁移

.native-select {
  display: inline-block;
  position: relative;
  width: 100%;
  
  &:hover {
    .native-select__inner {
      border-color: #c0c4cc;
    }
  }
  
  // 多选标签样式
  .native-select__tags {
    position: relative;
    line-height: normal;
    white-space: normal;
    z-index: 1;
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    height: 26px;
    overflow: hidden;
    
    .native-select__tag {
      box-sizing: border-box;
      border-color: transparent;
      margin: 2px 0 2px 6px;
      background-color: #f0f2f5;
      display: flex;
      max-width: 100%;
      align-items: center;
      border: none;
      background: #EEEEEE;
      border-radius: 5px;
      padding: 0 8px;
      height: 24px;
      font-size: 12px;
      
      .native-select__tag-text {
        overflow: hidden;
        text-overflow: ellipsis;
        color: #000;
        white-space: nowrap;
      }
      
      .hos-icon-close {
        margin-left: 6px;
        color: #909399;
        cursor: pointer;
        font-size: 14px;
        
        &:hover {
          color: #606266;
        }
      }
    }
  }
  
  // 多选输入框
  .native-select__input {
    border: none;
    outline: none;
    padding: 0;
    margin-left: 5px;
    color: #606266;
    font-size: 14px;
    appearance: none;
    height: 24px;
    line-height: 24px;
    background-color: transparent;
    min-width: 80px;
    flex: 1;
    
    &.is-mini {
      height: 14px;
    }
  }
  
  // 单选输入框包装器
  .native-select__input-wrapper {
    position: relative;
    
    &.is-focus {
      .native-select__inner {
        border-color: #409eff;
      }
    }
  }
  
  // 主输入框
  .native-select__inner {
    -webkit-appearance: none;
    background-color: #fff;
    background-image: none;
    border-radius: 4px;
    border: 1px solid #dcdfe6;
    box-sizing: border-box;
    color: #606266;
    display: inline-block;
    font-size: 14px;
    height: 28px;
    line-height: 28px;
    outline: none;
    padding: 0 15px;
    transition: border-color .2s cubic-bezier(.645,.045,.355,1);
    width: 100%;
    cursor: pointer;
    padding-right: 35px;
    
    &:focus {
      border-color: #409eff;
    }
    
    &:disabled {
      cursor: not-allowed;
      background-color: #f5f7fa;
      color: #c0c4cc;
      
      &:hover {
        border-color: #dcdfe6;
      }
    }
  }
  
  // 下拉箭头
  .native-select__caret {
    position: absolute;
    right: 15px;
    top: 50%;
    transform: translateY(-50%);
    color: #b6c9ee;
    font-size: 14px;
    transition: transform .3s;
    transform: translateY(-50%) rotateZ(180deg);
    cursor: pointer;
    
    &.is-reverse {
      transform: translateY(-50%) rotateZ(0deg);
    }
  }
  
  // 清空按钮
  .native-select__clear {
    position: absolute;
    right: 15px;
    top: 50%;
    transform: translateY(-50%);
    color: #c0c4cc;
    font-size: 14px;
    cursor: pointer;
    transition: color .2s cubic-bezier(.645,.045,.355,1);
    
    &:hover {
      color: #909399;
    }
  }
}

// 下拉菜单样式
.native-select-dropdown {
  position: absolute;
  z-index: 2001;
  border-radius: 4px;
  background-color: #fff;
  box-shadow: 0px 0px 4px 1px #DBDBDB;
  box-sizing: border-box;
  margin: 5px 0;
  top: 100%;
  left: 0;
  right: 0;
  min-width: 200px; /* 设置最小宽度确保内容有足够空间显示 */
  
  .native-select-dropdown__wrap {
    max-height: 274px;
    overflow-y: auto;
  }
  
  .native-select-dropdown__list {
    list-style: none;
    padding: 6px 0;
    margin: 0;
    box-sizing: border-box;
    
    &.is-empty {
      padding: 0;
    }
  }
  
  .native-select-dropdown__item {
    font-size: 14px;
    padding: 0 20px;
    position: relative;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    color: #606266;
    height: 34px;
    line-height: 34px;
    box-sizing: border-box;
    cursor: pointer;
    max-width: 280px; /* 设置最大宽度 */
    min-width: 180px; /* 设置最小宽度 */
    
    &.selected {
      color: #409eff;
      font-weight: bold;
    }
    
    &.is-disabled {
      color: #c0c4cc;
      cursor: not-allowed;
      
      &:hover {
        background-color: #fff;
      }
    }
    
    &.hover,
    &:hover {
      background-color: #f5f7fa;
    }
  }
  
  .native-select-dropdown__empty {
    padding: 10px 0;
    margin: 0;
    text-align: center;
    color: #999;
    font-size: 14px;
  }
}

// 过渡动画
.native-zoom-in-top-enter-active,
.native-zoom-in-top-leave-active {
  opacity: 1;
  transform: scaleY(1);
  transition: transform 300ms cubic-bezier(0.23, 1, 0.32, 1), opacity 300ms cubic-bezier(0.23, 1, 0.32, 1);
  transform-origin: center top;
}

.native-zoom-in-top-enter,
.native-zoom-in-top-leave-active {
  opacity: 0;
  transform: scaleY(0);
}

// 多选模式
.native-select-multiple {
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  min-height: 28px;
  height: 28px;
  padding: 0 15px 0 5px;
  background-color: #fff;
  transition: border-color .2s cubic-bezier(.645,.045,.355,1);
  box-sizing: border-box;
  
  &:hover {
    border-color: #c0c4cc;
  }
  
  &.is-focus {
    border-color: #409eff;
  }
  
  .native-select-dropdown__item {
    padding-right: 40px;
    
    &.selected {
      color: #409eff;
      background-color: #fff;
      
      &.hover {
        background-color: #f5f7fa;
      }
      
      &::after {
        position: absolute;
        right: 20px;
        font-family: 'element-icons';
        content: "\e6da";
        font-size: 12px;
        font-weight: bold;
        -webkit-font-smoothing: antialiased;
        -moz-osx-font-smoothing: grayscale;
      }
    }
  }
}

// 尺寸变体
.native-select--medium {
  .native-select__inner {
    height: 36px;
    line-height: 36px;
  }
}

.native-select--small {
  .native-select__inner {
    height: 32px;
    line-height: 32px;
  }
}

.native-select--mini {
  .native-select__inner {
    height: 28px;
    line-height: 28px;
  }
}