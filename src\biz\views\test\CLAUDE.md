# 查询组件重构任务说明

## 项目背景

本任务是重构现有的查询组件(QueryBuilder)，将其中使用的hos-xxx UI组件替换为原生HTML实现，以减少对hosui组件库的依赖。

## 当前状态

- ✅ 已实现QueryBuilder主组件
- ✅ 已实现QueryBuilderGroup、QueryBuilderRule等核心组件
- ✅ 已创建native-components目录，包含NativeSelect、NativeInput等原生组件实现
- ✅ 已完成大部分核心功能的迁移
- ❓ 存在一些需要修复的bug，需要继续调试和完善

## 技术原则

1. **优先参考hosui源码** - 在转换时必须参考node_modules\hosui目录下的组件源码实现
2. **样式完全一致** - 确保替换后的样式与原hos-ui组件完全相同
3. **业务逻辑不变** - 不改变任何业务代码，保持API兼容性
4. **功能完整性** - 确保所有原有功能正常工作

## 文件结构

```
src/biz/views/test/
├── QueryBuilder.vue              # 主查询构建器组件
├── QueryBuilderGroup.vue         # 查询条件组
├── QueryBuilderRule.vue          # 单个查询规则
├── QueryBuilderSubGroup.vue      # 子查询组
├── RulesGroupLogical.vue         # 逻辑操作符组件
├── SearchItemSelect.vue          # 查询项选择组件
├── ScrollMenu.vue               # 滚动菜单
├── hosui/                       # hosui组件库源码（用于参考）
├── native-components/           # 原生组件实现
│   ├── base/                    # 基础组件
│   │   ├── NativeSelect.vue     # 原生选择器
│   │   ├── NativeInput.vue      # 原生输入框
│   │   └── ...
│   └── styles/                  # 样式文件
└── mapData.js                   # 数据映射配置
```

## 关键替换组件

- `hos-select` → `NativeSelect.vue`
- `hos-option` → `NativeOption.vue`
- `hos-input` → `NativeInput.vue`
- `hos-button` → `NativeButton.vue`
- `hos-dialog` → `NativeDialog.vue`

## 待修复问题

根据用户反馈，当前存在一些需要修复的bug。主要关注：

1. 选择器功能是否正常工作
2. 查询条件的添加、删除、修改
3. 数据绑定和事件处理
4. 样式显示问题

## 开发指导

1. **查看bug详情** - 首先了解具体的bug现象和错误信息
2. **对比原始实现** - 参考hosui源码中的相同组件实现
3. **保持API兼容** - 确保props、events、methods与原hos-ui组件一致
4. **样式精确匹配** - 从hosui/lib/theme-chalk/目录提取准确的CSS样式
5. **功能验证** - 修复后进行完整的功能测试

## 调试说明

当需要调试时，应该：

1. 先识别具体的问题现象
2. 定位到相关的组件文件
3. 对比hosui源码实现
4. 进行针对性修复
5. 验证修复效果

## 注意事项

- 不要破坏现有的业务逻辑
- 保持与hosui组件的完全兼容性
- 确保样式的像素级精确匹配
- 处理好事件冒泡和数据流转

