<template>
  <div v-if="visible">
    <!-- 遮罩层 -->
    <div v-if="modal" class="v-modal" @click="handleModalClick"></div>

    <!-- 对话框 -->
    <transition name="dialog-fade" @after-enter="afterEnter" @after-leave="afterLeave">
      <div
        v-show="visible"
        class="native-dialog__wrapper"
        @click.self="handleWrapperClick"
      >
        <!-- 对话框主体 -->
        <div
          ref="dialog"
          :class="dialogClasses"
          :style="dialogStyle"
          role="dialog"
          aria-modal="true"
          :aria-label="title || 'dialog'"
        >
          <!-- 头部 -->
          <div v-if="showHeader || showClose" class="native-dialog__header">
            <slot name="title">
              <span v-if="title" class="native-dialog__title">{{ title }}</span>
            </slot>

            <!-- 关闭按钮 -->
            <button
              v-if="showClose"
              type="button"
              class="native-dialog__headerbtn"
              aria-label="Close"
              @click="handleClose"
            >
              <i class="native-dialog__close"></i>
            </button>
          </div>

          <!-- 内容区域 -->
          <div v-if="rendered" class="native-dialog__body">
            <slot></slot>
          </div>

          <!-- 底部 -->
          <div v-if="$slots.footer" class="native-dialog__footer">
            <slot name="footer"></slot>
          </div>
        </div>
      </div>
    </transition>
  </div>
</template>

<script>
export default {
  name: 'NativeDialog',
  props: {
    visible: Boolean,
    title: String,
    width: {
      type: String,
      default: '50%'
    },
    fullscreen: Boolean,
    top: {
      type: String,
      default: '15vh'
    },
    modal: {
      type: Boolean,
      default: true
    },
    modalAppendToBody: {
      type: Boolean,
      default: true
    },
    appendToBody: {
      type: Boolean,
      default: false
    },
    lockScroll: {
      type: Boolean,
      default: true
    },
    customClass: String,
    closeOnClickModal: {
      type: Boolean,
      default: true
    },
    closeOnPressEscape: {
      type: Boolean,
      default: true
    },
    showClose: {
      type: Boolean,
      default: true
    },
    beforeClose: Function,
    center: Boolean,
    destroyOnClose: Boolean
  },
  data() {
    return {
      closed: false,
      rendered: false
    }
  },
  computed: {
    dialogClasses() {
      return [
        'native-dialog',
        {
          'is-fullscreen': this.fullscreen,
          'native-dialog--center': this.center
        },
        this.customClass
      ]
    },
    dialogStyle() {
      const style = {}
      if (!this.fullscreen) {
        style.marginTop = this.top
        if (this.width) {
          style.width = this.width
        }
      }
      return style
    },
    showHeader() {
      return this.title || this.$slots.title
    }
  },
  watch: {
    visible(val) {
      if (val) {
        this.closed = false
        this.rendered = true
        this.$emit('open')
        this.$el.addEventListener('scroll', this.updatePopper)
        this.$nextTick(() => {
          this.$refs.dialog.scrollTop = 0
        })
        if (this.appendToBody) {
          document.body.appendChild(this.$el)
        }
      } else {
        this.$el.removeEventListener('scroll', this.updatePopper)
        if (!this.closed) this.$emit('close')
        if (this.destroyOnClose) {
          this.$nextTick(() => {
            this.rendered = false
          })
        }
      }
    }
  },
  mounted() {
    if (this.visible) {
      this.rendered = true
      this.open()
      if (this.appendToBody) {
        document.body.appendChild(this.$el)
      }
    }
    document.addEventListener('keydown', this.handleKeydown)
  },
  beforeDestroy() {
    if (this.appendToBody && this.$el && this.$el.parentNode) {
      this.$el.parentNode.removeChild(this.$el)
    }
    document.removeEventListener('keydown', this.handleKeydown)
  },
  methods: {
    handleWrapperClick() {
      if (!this.closeOnClickModal) return
      this.handleClose()
    },
    handleModalClick() {
      if (!this.closeOnClickModal) return
      this.handleClose()
    },
    handleClose() {
      if (typeof this.beforeClose === 'function') {
        this.beforeClose(this.hide)
      } else {
        this.hide()
      }
    },
    hide(cancel) {
      if (cancel !== false) {
        this.$emit('update:visible', false)
        this.$emit('close')
        this.closed = true
      }
    },
    updatePopper() {
      // 更新弹出层位置的占位方法
    },
    afterEnter() {
      this.$emit('opened')
    },
    afterLeave() {
      this.$emit('closed')
    },
    open() {
      this.rendered = true
    },
    handleKeydown(event) {
      if (this.closeOnPressEscape && event.keyCode === 27) {
        this.handleClose()
      }
    }
  }
}
</script>

<style lang="scss">
// 引入样式文件 - 注意：移除 scoped，因为蒙层需要全局样式
@import '../styles/dialog.scss';
</style>
